{"name": "ai-gateway-admin", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "eslint", "deploy": "npm run build && npx wrangler pages deploy out --project-name=ai-gateway-admin", "deploy:prod": "npm run build && npx wrangler pages deploy out --project-name=ai-gateway-admin --env=production"}, "dependencies": {"@headlessui/react": "^2.2.7", "@heroicons/react": "^2.2.0", "lucide-react": "^0.542.0", "next": "15.5.2", "react": "19.1.0", "react-dom": "19.1.0", "recharts": "^3.1.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.5.2", "tailwindcss": "^4", "typescript": "^5"}}